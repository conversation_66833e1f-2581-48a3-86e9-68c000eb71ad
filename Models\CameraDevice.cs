using System;
using System.ComponentModel;
using MvFGCtrlC.NET;

namespace MultipleCamera.Models
{
    public class CameraDevice : INotifyPropertyChanged
    {
        private uint _index;
        private uint _listIndex;
        private string _modelName;
        private string _serialNumber;
        private uint _deviceType;
        private bool _isOpen;
        private bool _isSelected;
        private bool _isGrabbing;
        private CDevice _cDevice;
        private CStream _cStream;

        public uint Index
        {
            get => _index;
            set
            {
                _index = value;
                OnPropertyChanged(nameof(Index));
            }
        }

        public uint ListIndex
        {
            get => _listIndex;
            set
            {
                _listIndex = value;
                OnPropertyChanged(nameof(ListIndex));
            }
        }

        public string ModelName
        {
            get => _modelName;
            set
            {
                _modelName = value;
                OnPropertyChanged(nameof(ModelName));
            }
        }

        public string SerialNumber
        {
            get => _serialNumber;
            set
            {
                _serialNumber = value;
                OnPropertyChanged(nameof(SerialNumber));
            }
        }

        public uint DeviceType
        {
            get => _deviceType;
            set
            {
                _deviceType = value;
                OnPropertyChanged(nameof(DeviceType));
            }
        }

        public bool IsOpen
        {
            get => _isOpen;
            set
            {
                _isOpen = value;
                OnPropertyChanged(nameof(IsOpen));
            }
        }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged(nameof(IsSelected));
            }
        }

        public bool IsGrabbing
        {
            get => _isGrabbing;
            set
            {
                _isGrabbing = value;
                OnPropertyChanged(nameof(IsGrabbing));
            }
        }

        public CDevice CDevice
        {
            get => _cDevice;
            set
            {
                _cDevice = value;
                OnPropertyChanged(nameof(CDevice));
            }
        }

        public CStream CStream
        {
            get => _cStream;
            set
            {
                _cStream = value;
                OnPropertyChanged(nameof(CStream));
            }
        }

        public string TypeDisplayName
        {
            get
            {
                switch (DeviceType)
                {
                    case CParamDefine.MV_FG_GEV_DEVICE:
                        return "GEV";
                    case CParamDefine.MV_FG_CXP_DEVICE:
                        return "CXP";
                    case CParamDefine.MV_FG_CAMERALINK_DEVICE:
                        return "CML";
                    case CParamDefine.MV_FG_XoF_DEVICE:
                        return "XoF";
                    default:
                        return "Unknown";
                }
            }
        }

        public string FullDisplayName => $"[{TypeDisplayName}] | {ModelName} | {SerialNumber}";

        public string DisplayName => $"Cam{Index + 1}";

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
