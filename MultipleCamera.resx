<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>相机初始化设置</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="checkBox7.Location" type="System.Drawing.Point, System.Drawing">
    <value>579, 266</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="groupBox3.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="&gt;&gt;checkBox5.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btnStartGrab.Text" xml:space="preserve">
    <value>开始采集</value>
  </data>
  <data name="&gt;&gt;checkBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pictureBox8.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBox3.Name" xml:space="preserve">
    <value>pictureBox3</value>
  </data>
  <data name="checkBox2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="pictureBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>23, 288</value>
  </data>
  <data name="&gt;&gt;btnEnumDevice.Name" xml:space="preserve">
    <value>btnEnumDevice</value>
  </data>
  <data name="&gt;&gt;listBox1.Name" xml:space="preserve">
    <value>listBox1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="&gt;&gt;btnCloseDevice.Name" xml:space="preserve">
    <value>btnCloseDevice</value>
  </data>
  <data name="&gt;&gt;btnCloseInterface.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="checkBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 16</value>
  </data>
  <data name="&gt;&gt;btnOpenDevice.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;btnCloseDevice.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;cmbInterfaceListSecond.Name" xml:space="preserve">
    <value>cmbInterfaceListSecond</value>
  </data>
  <data name="checkBox8.Text" xml:space="preserve">
    <value>Cam4</value>
  </data>
  <data name="checkBox1.Text" xml:space="preserve">
    <value>Cam1</value>
  </data>
  <data name="checkBox5.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="groupBox3.Text" xml:space="preserve">
    <value>采集控制</value>
  </data>
  <data name="pictureBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>231, 151</value>
  </data>
  <data name="checkBox6.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;cmbInterfaceListSecond.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="pictureBox1.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;checkBox7.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="checkBox4.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="checkBox7.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="checkBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 16</value>
  </data>
  <data name="&gt;&gt;checkBox4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="btnOpenDevice.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 58</value>
  </data>
  <data name="&gt;&gt;btnCloseDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;btnStopGrab.Name" xml:space="preserve">
    <value>btnStopGrab</value>
  </data>
  <data name="checkBox6.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="&gt;&gt;pictureBox7.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="pictureBox7.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="&gt;&gt;btnCloseInterface.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="checkBox4.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 16</value>
  </data>
  <data name="checkBox8.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 16</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="pictureBox5.Size" type="System.Drawing.Size, System.Drawing">
    <value>231, 151</value>
  </data>
  <data name="&gt;&gt;checkBox6.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btnEnumDevice.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>21, 27</value>
  </data>
  <data name="listBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>23, 474</value>
  </data>
  <data name="btnOpenDevice.Text" xml:space="preserve">
    <value>打开相机</value>
  </data>
  <data name="&gt;&gt;pictureBox8.Name" xml:space="preserve">
    <value>pictureBox8</value>
  </data>
  <data name="&gt;&gt;pictureBox6.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="pictureBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>23, 109</value>
  </data>
  <data name="listBox1.ItemHeight" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;pictureBox3.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="&gt;&gt;checkBox5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="cmbInterfaceListFirst.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 20</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>MultipleCamera</value>
  </data>
  <data name="btnStopGrab.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 87</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 12</value>
  </data>
  <data name="checkBox4.Text" xml:space="preserve">
    <value>Cam4</value>
  </data>
  <data name="&gt;&gt;pictureBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnEnumInterface.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="btnCloseDevice.Text" xml:space="preserve">
    <value>关闭相机</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>采集卡列表1</value>
  </data>
  <data name="&gt;&gt;btnEnumDevice.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="btnEnumInterface.Location" type="System.Drawing.Point, System.Drawing">
    <value>23, 21</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>MultipleCamera演示程序</value>
  </data>
  <data name="btnEnumInterface.Text" xml:space="preserve">
    <value>枚举采集卡</value>
  </data>
  <data name="&gt;&gt;pictureBox4.Name" xml:space="preserve">
    <value>pictureBox4</value>
  </data>
  <data name="&gt;&gt;checkBox7.Name" xml:space="preserve">
    <value>checkBox7</value>
  </data>
  <data name="&gt;&gt;pictureBox3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="groupBox2.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;checkBox2.Name" xml:space="preserve">
    <value>checkBox2</value>
  </data>
  <data name="pictureBox7.Size" type="System.Drawing.Size, System.Drawing">
    <value>231, 151</value>
  </data>
  <data name="btnCloseInterface.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="checkBox3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox5.Text" xml:space="preserve">
    <value>Cam1</value>
  </data>
  <data name="&gt;&gt;btnOpenInterface.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="groupBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>1090, 374</value>
  </data>
  <data name="&gt;&gt;btnEnumInterface.Name" xml:space="preserve">
    <value>btnEnumInterface</value>
  </data>
  <data name="&gt;&gt;pictureBox4.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btnOpenInterface.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 23</value>
  </data>
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;checkBox2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="checkBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>23, 87</value>
  </data>
  <data name="checkBox7.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 16</value>
  </data>
  <data name="checkBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>276, 266</value>
  </data>
  <data name="&gt;&gt;checkBox3.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBox7.Name" xml:space="preserve">
    <value>pictureBox7</value>
  </data>
  <data name="cmbInterfaceListFirst.Location" type="System.Drawing.Point, System.Drawing">
    <value>98, 24</value>
  </data>
  <data name="pictureBox8.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>169, 139</value>
  </data>
  <data name="&gt;&gt;checkBox1.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="&gt;&gt;btnStopGrab.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox3.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="checkBox2.Text" xml:space="preserve">
    <value>Cam2</value>
  </data>
  <data name="checkBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>23, 266</value>
  </data>
  <data name="&gt;&gt;checkBox1.Name" xml:space="preserve">
    <value>checkBox1</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="pictureBox8.Location" type="System.Drawing.Point, System.Drawing">
    <value>832, 288</value>
  </data>
  <data name="&gt;&gt;listBox1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnEnumInterface.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;pictureBox8.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;pictureBox6.Name" xml:space="preserve">
    <value>pictureBox6</value>
  </data>
  <data name="pictureBox2.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;checkBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox5.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="checkBox1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;checkBox4.Name" xml:space="preserve">
    <value>checkBox4</value>
  </data>
  <data name="label2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="&gt;&gt;checkBox8.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="checkBox8.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;checkBox4.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="checkBox3.Text" xml:space="preserve">
    <value>Cam3</value>
  </data>
  <data name="&gt;&gt;btnCloseInterface.Name" xml:space="preserve">
    <value>btnCloseInterface</value>
  </data>
  <data name="pictureBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>231, 151</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btnCloseInterface.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 23</value>
  </data>
  <data name="btnStopGrab.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 23</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>1090, 207</value>
  </data>
  <data name="&gt;&gt;btnOpenDevice.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;pictureBox8.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pictureBox5.Name" xml:space="preserve">
    <value>pictureBox5</value>
  </data>
  <data name="&gt;&gt;btnCloseInterface.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;cmbInterfaceListSecond.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 12</value>
  </data>
  <data name="btnEnumDevice.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 23</value>
  </data>
  <data name="pictureBox5.Location" type="System.Drawing.Point, System.Drawing">
    <value>579, 109</value>
  </data>
  <data name="&gt;&gt;btnStartGrab.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;groupBox2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="checkBox5.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btnOpenInterface.Text" xml:space="preserve">
    <value>打开采集卡</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkBox6.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnOpenInterface.Name" xml:space="preserve">
    <value>btnOpenInterface</value>
  </data>
  <data name="&gt;&gt;pictureBox7.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;checkBox2.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="&gt;&gt;cmbInterfaceListFirst.Name" xml:space="preserve">
    <value>cmbInterfaceListFirst</value>
  </data>
  <data name="pictureBox4.Size" type="System.Drawing.Size, System.Drawing">
    <value>231, 151</value>
  </data>
  <data name="&gt;&gt;checkBox7.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="checkBox4.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox8.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="&gt;&gt;btnEnumInterface.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="checkBox6.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 16</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="btnEnumDevice.Text" xml:space="preserve">
    <value>枚举相机</value>
  </data>
  <data name="&gt;&gt;checkBox3.Name" xml:space="preserve">
    <value>checkBox3</value>
  </data>
  <data name="&gt;&gt;cmbInterfaceListFirst.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBox2.Name" xml:space="preserve">
    <value>pictureBox2</value>
  </data>
  <data name="btnOpenDevice.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;pictureBox7.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pictureBox6.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="btnStartGrab.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 42</value>
  </data>
  <data name="checkBox6.Text" xml:space="preserve">
    <value>Cam2</value>
  </data>
  <data name="&gt;&gt;groupBox3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="btnCloseDevice.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 23</value>
  </data>
  <data name="pictureBox5.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="btnOpenDevice.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 23</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>1090, 49</value>
  </data>
  <data name="checkBox8.Location" type="System.Drawing.Point, System.Drawing">
    <value>832, 266</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>169, 139</value>
  </data>
  <data name="&gt;&gt;groupBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnOpenInterface.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;checkBox4.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox8.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="pictureBox6.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;groupBox2.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Name" xml:space="preserve">
    <value>pictureBox1</value>
  </data>
  <data name="&gt;&gt;pictureBox5.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;pictureBox6.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>采集卡列表2</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>577, 27</value>
  </data>
  <data name="checkBox7.Text" xml:space="preserve">
    <value>Cam3</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="checkBox5.Location" type="System.Drawing.Point, System.Drawing">
    <value>579, 87</value>
  </data>
  <data name="pictureBox3.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;btnEnumDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="pictureBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>276, 109</value>
  </data>
  <data name="&gt;&gt;cmbInterfaceListFirst.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="&gt;&gt;btnCloseDevice.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="groupBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>169, 139</value>
  </data>
  <data name="&gt;&gt;pictureBox5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="listBox1.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="&gt;&gt;listBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox5.Name" xml:space="preserve">
    <value>checkBox5</value>
  </data>
  <data name="checkBox1.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;checkBox6.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;btnEnumDevice.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="pictureBox8.Size" type="System.Drawing.Size, System.Drawing">
    <value>231, 151</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnStopGrab.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;groupBox3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;checkBox8.Name" xml:space="preserve">
    <value>checkBox8</value>
  </data>
  <data name="&gt;&gt;btnOpenDevice.Name" xml:space="preserve">
    <value>btnOpenDevice</value>
  </data>
  <data name="btnOpenInterface.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnCloseInterface.Text" xml:space="preserve">
    <value>关闭采集卡</value>
  </data>
  <data name="btnStartGrab.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;btnStartGrab.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="btnEnumDevice.Location" type="System.Drawing.Point, System.Drawing">
    <value>23, 21</value>
  </data>
  <data name="pictureBox7.Location" type="System.Drawing.Point, System.Drawing">
    <value>579, 288</value>
  </data>
  <data name="checkBox6.Location" type="System.Drawing.Point, System.Drawing">
    <value>832, 87</value>
  </data>
  <data name="checkBox7.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox2.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="pictureBox6.Location" type="System.Drawing.Point, System.Drawing">
    <value>832, 109</value>
  </data>
  <data name="btnOpenInterface.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 58</value>
  </data>
  <data name="cmbInterfaceListFirst.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;btnStopGrab.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnEnumInterface.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 31</value>
  </data>
  <data name="&gt;&gt;groupBox2.Name" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;pictureBox3.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="pictureBox6.Size" type="System.Drawing.Size, System.Drawing">
    <value>231, 151</value>
  </data>
  <data name="&gt;&gt;btnStartGrab.Name" xml:space="preserve">
    <value>btnStartGrab</value>
  </data>
  <data name="&gt;&gt;checkBox8.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnEnumInterface.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="pictureBox4.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;pictureBox1.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="&gt;&gt;pictureBox2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="btnStartGrab.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 23</value>
  </data>
  <data name="&gt;&gt;btnStartGrab.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btnStopGrab.Text" xml:space="preserve">
    <value>停止采集</value>
  </data>
  <data name="btnCloseDevice.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;listBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pictureBox4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="btnCloseInterface.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 98</value>
  </data>
  <data name="&gt;&gt;cmbInterfaceListSecond.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="pictureBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>276, 288</value>
  </data>
  <data name="&gt;&gt;checkBox7.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1271, 622</value>
  </data>
  <data name="&gt;&gt;checkBox3.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="checkBox3.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="listBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>857, 136</value>
  </data>
  <data name="btnStopGrab.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbInterfaceListSecond.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 20</value>
  </data>
  <data name="&gt;&gt;btnOpenInterface.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cmbInterfaceListSecond.Location" type="System.Drawing.Point, System.Drawing">
    <value>654, 24</value>
  </data>
  <data name="&gt;&gt;pictureBox2.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>采集卡初始化设置</value>
  </data>
  <data name="&gt;&gt;pictureBox5.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="cmbInterfaceListSecond.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnCloseDevice.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 98</value>
  </data>
  <data name="&gt;&gt;groupBox3.Name" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;cmbInterfaceListFirst.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="pictureBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>231, 151</value>
  </data>
  <data name="checkBox5.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 16</value>
  </data>
  <data name="&gt;&gt;checkBox6.Name" xml:space="preserve">
    <value>checkBox6</value>
  </data>
  <data name="&gt;&gt;btnOpenDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="checkBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>276, 87</value>
  </data>
  <data name="&gt;&gt;checkBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBox4.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="checkBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 16</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Chinese (Simplified)</value>
  </metadata>
</root>