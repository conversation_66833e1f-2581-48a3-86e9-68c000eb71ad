using System;
using System.ComponentModel;
using MvFGCtrlC.NET;

namespace MultipleCamera.Models
{
    public class CameraInterface : INotifyPropertyChanged
    {
        private uint _index;
        private string _displayName;
        private string _interfaceId;
        private string _serialNumber;
        private uint _interfaceType;
        private bool _isOpen;
        private CInterface _cInterface;

        public uint Index
        {
            get => _index;
            set
            {
                _index = value;
                OnPropertyChanged(nameof(Index));
            }
        }

        public string DisplayName
        {
            get => _displayName;
            set
            {
                _displayName = value;
                OnPropertyChanged(nameof(DisplayName));
            }
        }

        public string InterfaceId
        {
            get => _interfaceId;
            set
            {
                _interfaceId = value;
                OnPropertyChanged(nameof(InterfaceId));
            }
        }

        public string SerialNumber
        {
            get => _serialNumber;
            set
            {
                _serialNumber = value;
                OnPropertyChanged(nameof(SerialNumber));
            }
        }

        public uint InterfaceType
        {
            get => _interfaceType;
            set
            {
                _interfaceType = value;
                OnPropertyChanged(nameof(InterfaceType));
            }
        }

        public bool IsOpen
        {
            get => _isOpen;
            set
            {
                _isOpen = value;
                OnPropertyChanged(nameof(IsOpen));
            }
        }

        public CInterface CInterface
        {
            get => _cInterface;
            set
            {
                _cInterface = value;
                OnPropertyChanged(nameof(CInterface));
            }
        }

        public string TypeDisplayName
        {
            get
            {
                switch (InterfaceType)
                {
                    case CParamDefine.MV_FG_GEV_INTERFACE:
                        return "GEV";
                    case CParamDefine.MV_FG_CXP_INTERFACE:
                        return "CXP";
                    case CParamDefine.MV_FG_CAMERALINK_INTERFACE:
                        return "CML";
                    case CParamDefine.MV_FG_XoF_INTERFACE:
                        return "XoF";
                    default:
                        return "Unknown";
                }
            }
        }

        public string FullDisplayName => $"{TypeDisplayName}[{Index}]: {DisplayName} | {InterfaceId} | {SerialNumber}";

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
