# Hướng dẫn Build và Chạy Project MultipleCamera WPF

## Tổng quan
Project đã được chuyển đổi từ WinForms sang WPF MVVM thành công. Dưới đây là hướng dẫn để build và chạy project.

## Yêu cầu hệ thống
- Visual Studio 2019 hoặc 2022 (Professional/Community)
- .NET Framework 4.8
- MvFGCtrlC.NET library (đã có trong project)

## Cách 1: Build bằng Visual Studio (Khuyến nghị)

### Bước 1: Mở Project
1. Double-click vào file `MultipleCamera.sln` để mở trong Visual Studio
2. Hoặc mở Visual Studio và chọn File → Open → Project/Solution → chọn `MultipleCamera.sln`

### Bước 2: Kiểm tra Configuration
1. Đảm bảo Configuration được set là **Debug**
2. Platform được set là **Any CPU** hoặc **x86**

### Bước 3: Restore NuGet Packages (nếu cần)
1. Right-click vào Solution trong Solution Explorer
2. Chọn "Restore NuGet Packages"

### Bước 4: Build Project
1. Nhấn **Ctrl+Shift+B** hoặc chọn Build → Build Solution
2. Kiểm tra Output window để xem kết quả build

### Bước 5: Chạy Project
1. Nhấn **F5** để chạy với debugging
2. Hoặc **Ctrl+F5** để chạy không debugging

## Cách 2: Build bằng Command Line

### Tìm MSBuild
Mở Command Prompt và chạy:
```cmd
"C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" MultipleCamera.sln /p:Configuration=Debug
```

Hoặc với Visual Studio 2019:
```cmd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" MultipleCamera.sln /p:Configuration=Debug
```

### Chạy Application
Sau khi build thành công:
```cmd
bin\Debug\MultipleCamera.exe
```

## Cấu trúc Project sau khi chuyển đổi

```
MultipleCamera/
├── Models/
│   ├── CameraInterface.cs    # Model cho camera interface
│   ├── CameraDevice.cs       # Model cho camera device
│   └── LogMessage.cs         # Model cho log messages
├── ViewModels/
│   ├── ViewModelBase.cs      # Base class cho ViewModels
│   └── MainViewModel.cs      # Main ViewModel với business logic
├── Views/
│   └── MainWindow.xaml       # Main UI window
├── Commands/
│   └── RelayCommand.cs       # ICommand implementation
├── App.xaml                  # WPF Application definition
├── App.xaml.cs              # Application code-behind
└── MultipleCamera.csproj     # Project file (đã cập nhật cho WPF)
```

## Các tính năng đã implement

### ✅ Hoàn thành:
- [x] MVVM Architecture
- [x] Data Binding
- [x] Command Pattern
- [x] Camera Interface Enumeration
- [x] Camera Device Management
- [x] Logging System
- [x] UI Layout với WPF

### 🔄 Cần hoàn thiện:
- [ ] Image Display (camera preview)
- [ ] Error Handling improvements
- [ ] UI Styling và Theming

## Troubleshooting

### Lỗi Build thường gặp:

1. **Missing References**
   - Đảm bảo MvFGCtrlC.NET library có trong project
   - Check References trong Solution Explorer

2. **Target Framework Issues**
   - Project đã được set cho .NET Framework 4.8
   - Đảm bảo máy có cài .NET Framework 4.8

3. **XAML Compilation Errors**
   - Kiểm tra syntax trong MainWindow.xaml
   - Đảm bảo data binding paths đúng

### Lỗi Runtime thường gặp:

1. **Camera Library Issues**
   - Đảm bảo camera hardware được kết nối
   - Check driver cho camera devices

2. **Threading Issues**
   - Camera operations chạy trên background threads
   - UI updates được dispatch về main thread

## Testing

### Test cơ bản:
1. Chạy application
2. Click "Enum Interface" để liệt kê camera interfaces
3. Chọn interface và click "Open Interface"
4. Click "Enum Device" để tìm camera devices
5. Chọn devices và click "Open Device"
6. Click "Start Grab" để bắt đầu capture

### Expected Behavior:
- Log messages hiển thị trong ListBox
- Device checkboxes enable/disable theo trạng thái
- Commands enable/disable dựa trên context

## Liên hệ
Nếu gặp vấn đề, hãy kiểm tra:
1. Build Output window trong Visual Studio
2. Error List window
3. Log messages trong application
