using System;
using System.ComponentModel;

namespace MultipleCamera.Models
{
    public class LogMessage : INotifyPropertyChanged
    {
        private DateTime _timestamp;
        private string _message;
        private LogLevel _level;

        public DateTime Timestamp
        {
            get => _timestamp;
            set
            {
                _timestamp = value;
                OnPropertyChanged(nameof(Timestamp));
                OnPropertyChanged(nameof(FormattedTimestamp));
            }
        }

        public string Message
        {
            get => _message;
            set
            {
                _message = value;
                OnPropertyChanged(nameof(Message));
            }
        }

        public LogLevel Level
        {
            get => _level;
            set
            {
                _level = value;
                OnPropertyChanged(nameof(Level));
            }
        }

        public string FormattedTimestamp => Timestamp.ToString("HH:mm:ss");

        public string FormattedMessage => $"[{FormattedTimestamp}] {Message}";

        public LogMessage()
        {
            Timestamp = DateTime.Now;
            Level = LogLevel.Info;
        }

        public LogMessage(string message, LogLevel level = LogLevel.Info)
        {
            Timestamp = DateTime.Now;
            Message = message;
            Level = level;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public enum LogLevel
    {
        Info,
        Warning,
        Error,
        Success
    }
}
