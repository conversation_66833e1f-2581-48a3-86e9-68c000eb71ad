<Window x:Class="MultipleCamera.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="clr-namespace:MultipleCamera.ViewModels"
        mc:Ignorable="d"
        Title="Multiple Camera Control - WPF MVVM" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen">
    
    <Window.DataContext>
        <vm:MainViewModel />
    </Window.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="200"/>
        </Grid.RowDefinitions>

        <!-- Control Panel -->
        <GroupBox Grid.Row="0" Header="Control Panel" Margin="5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Interface Controls -->
                <StackPanel Grid.Row="0" Grid.ColumnSpan="2" Orientation="Horizontal" Margin="5">
                    <Button Content="Enum Interface" Command="{Binding EnumInterfaceCommand}" Margin="5" Padding="10,5"/>
                    <Button Content="Open Interface" Command="{Binding OpenInterfaceCommand}" Margin="5" Padding="10,5"/>
                    <Button Content="Close Interface" Command="{Binding CloseInterfaceCommand}" Margin="5" Padding="10,5"/>
                </StackPanel>

                <!-- Interface Selection -->
                <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                    <Label Content="Interface List 1:"/>
                    <ComboBox ItemsSource="{Binding AvailableInterfaces}" 
                              SelectedItem="{Binding SelectedInterface1}"
                              DisplayMemberPath="FullDisplayName"
                              IsEnabled="{Binding CanOpenInterface}"/>
                </StackPanel>

                <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                    <Label Content="Interface List 2:"/>
                    <ComboBox ItemsSource="{Binding AvailableInterfaces}" 
                              SelectedItem="{Binding SelectedInterface2}"
                              DisplayMemberPath="FullDisplayName"
                              IsEnabled="{Binding CanOpenInterface}"/>
                </StackPanel>

                <!-- Device Controls -->
                <StackPanel Grid.Row="2" Grid.ColumnSpan="2" Orientation="Horizontal" Margin="5">
                    <Button Content="Enum Device" Command="{Binding EnumDeviceCommand}" Margin="5" Padding="10,5"/>
                    <Button Content="Open Device" Command="{Binding OpenDeviceCommand}" Margin="5" Padding="10,5"/>
                    <Button Content="Close Device" Command="{Binding CloseDeviceCommand}" Margin="5" Padding="10,5"/>
                    <Button Content="Start Grab" Command="{Binding StartGrabCommand}" Margin="5" Padding="10,5"/>
                    <Button Content="Stop Grab" Command="{Binding StopGrabCommand}" Margin="5" Padding="10,5"/>
                </StackPanel>
            </Grid>
        </GroupBox>

        <!-- Camera Display Area -->
        <Grid Grid.Row="1" Margin="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- List 1 Cameras -->
            <GroupBox Grid.Column="0" Header="Camera List 1" Margin="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Device Selection -->
                    <ItemsControl Grid.Row="0" ItemsSource="{Binding DevicesList1}" Margin="5">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <StackPanel Orientation="Horizontal"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <CheckBox Content="{Binding DisplayName}"
                                          IsChecked="{Binding IsSelected}"
                                          IsEnabled="{Binding DataContext.CanOpenDevice, RelativeSource={RelativeSource AncestorType=Window}}"
                                          Margin="5"/>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>

                    <!-- Camera Display Grid -->
                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Row="0" Grid.Column="0" BorderBrush="Gray" BorderThickness="1" Margin="2">
                            <Image Name="CameraImage1_1" Stretch="Uniform"/>
                        </Border>
                        <Border Grid.Row="0" Grid.Column="1" BorderBrush="Gray" BorderThickness="1" Margin="2">
                            <Image Name="CameraImage1_2" Stretch="Uniform"/>
                        </Border>
                        <Border Grid.Row="1" Grid.Column="0" BorderBrush="Gray" BorderThickness="1" Margin="2">
                            <Image Name="CameraImage1_3" Stretch="Uniform"/>
                        </Border>
                        <Border Grid.Row="1" Grid.Column="1" BorderBrush="Gray" BorderThickness="1" Margin="2">
                            <Image Name="CameraImage1_4" Stretch="Uniform"/>
                        </Border>
                    </Grid>
                </Grid>
            </GroupBox>

            <!-- List 2 Cameras -->
            <GroupBox Grid.Column="1" Header="Camera List 2" Margin="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Device Selection -->
                    <ItemsControl Grid.Row="0" ItemsSource="{Binding DevicesList2}" Margin="5">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <StackPanel Orientation="Horizontal"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <CheckBox Content="{Binding DisplayName}"
                                          IsChecked="{Binding IsSelected}"
                                          IsEnabled="{Binding DataContext.CanOpenDevice, RelativeSource={RelativeSource AncestorType=Window}}"
                                          Margin="5"/>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>

                    <!-- Camera Display Grid -->
                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Row="0" Grid.Column="0" BorderBrush="Gray" BorderThickness="1" Margin="2">
                            <Image Name="CameraImage2_1" Stretch="Uniform"/>
                        </Border>
                        <Border Grid.Row="0" Grid.Column="1" BorderBrush="Gray" BorderThickness="1" Margin="2">
                            <Image Name="CameraImage2_2" Stretch="Uniform"/>
                        </Border>
                        <Border Grid.Row="1" Grid.Column="0" BorderBrush="Gray" BorderThickness="1" Margin="2">
                            <Image Name="CameraImage2_3" Stretch="Uniform"/>
                        </Border>
                        <Border Grid.Row="1" Grid.Column="1" BorderBrush="Gray" BorderThickness="1" Margin="2">
                            <Image Name="CameraImage2_4" Stretch="Uniform"/>
                        </Border>
                    </Grid>
                </Grid>
            </GroupBox>
        </Grid>

        <!-- Log Messages -->
        <GroupBox Grid.Row="2" Header="Log Messages" Margin="5">
            <ListBox ItemsSource="{Binding LogMessages}" 
                     ScrollViewer.HorizontalScrollBarVisibility="Auto"
                     ScrollViewer.VerticalScrollBarVisibility="Auto">
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding FormattedMessage}"/>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
        </GroupBox>
    </Grid>
</Window>
