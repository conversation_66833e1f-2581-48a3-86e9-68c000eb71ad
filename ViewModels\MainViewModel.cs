using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows;
using MultipleCamera.Commands;
using MultipleCamera.Models;
using MvFGCtrlC.NET;

namespace MultipleCamera.ViewModels
{
    public class MainViewModel : ViewModelBase
    {
        private const uint LIST_NUM = 2;
        private const uint MAX_DEVICE_NUM = 4;
        private const uint BUFFER_NUMBER = 3;
        private const uint TRIGGER_MODE_ON = 1;
        private const uint TRIGGER_MODE_OFF = 0;

        private readonly CSystem _cSystem = new CSystem();
        private readonly CInterface[] _cInterface = { null, null };
        private readonly CDevice[,] _cDevice = { { null, null, null, null }, { null, null, null, null } };
        private readonly CStream[,] _cStream = { { null, null, null, null }, { null, null, null, null } };
        private readonly Thread[,] _hGrabThread = { { null, null, null, null }, { null, null, null, null } };
        private readonly bool[,] _bGrabThreadFlag = { { false, false, false, false }, { false, false, false, false } };

        private uint _interfaceNum;
        private bool[] _isInterfaceOpen = { false, false };
        private bool[] _isDeviceOpen = { false, false };
        private bool _isGrabbing;
        private int _currentListIndex = -1;
        private int _currentCameraIndex = -1;

        private CameraInterface _selectedInterface1;
        private CameraInterface _selectedInterface2;

        public ObservableCollection<CameraInterface> AvailableInterfaces { get; }
        public ObservableCollection<CameraDevice> DevicesList1 { get; }
        public ObservableCollection<CameraDevice> DevicesList2 { get; }
        public ObservableCollection<LogMessage> LogMessages { get; }

        public CameraInterface SelectedInterface1
        {
            get => _selectedInterface1;
            set => SetProperty(ref _selectedInterface1, value);
        }

        public CameraInterface SelectedInterface2
        {
            get => _selectedInterface2;
            set => SetProperty(ref _selectedInterface2, value);
        }

        public bool IsGrabbing
        {
            get => _isGrabbing;
            set => SetProperty(ref _isGrabbing, value);
        }

        public bool CanEnumInterface => !(_isInterfaceOpen[0] || _isInterfaceOpen[1]);
        public bool CanOpenInterface => _interfaceNum > 0 && !(_isInterfaceOpen[0] || _isInterfaceOpen[1]);
        public bool CanCloseInterface => (_isInterfaceOpen[0] || _isInterfaceOpen[1]) && !(_isDeviceOpen[0] || _isDeviceOpen[1]);
        public bool CanEnumDevice => (_isInterfaceOpen[0] || _isInterfaceOpen[1]) && !(_isDeviceOpen[0] || _isDeviceOpen[1]);
        public bool CanOpenDevice => (_isInterfaceOpen[0] || _isInterfaceOpen[1]) && _interfaceNum > 0 && !(_isDeviceOpen[0] || _isDeviceOpen[1]);
        public bool CanCloseDevice => (_isInterfaceOpen[0] || _isInterfaceOpen[1]) && (_isDeviceOpen[0] || _isDeviceOpen[1]);
        public bool CanStartGrab => (_isDeviceOpen[0] || _isDeviceOpen[1]) && !_isGrabbing;
        public bool CanStopGrab => (_isDeviceOpen[0] || _isDeviceOpen[1]) && _isGrabbing;

        public ICommand EnumInterfaceCommand { get; }
        public ICommand OpenInterfaceCommand { get; }
        public ICommand CloseInterfaceCommand { get; }
        public ICommand EnumDeviceCommand { get; }
        public ICommand OpenDeviceCommand { get; }
        public ICommand CloseDeviceCommand { get; }
        public ICommand StartGrabCommand { get; }
        public ICommand StopGrabCommand { get; }

        public MainViewModel()
        {
            AvailableInterfaces = new ObservableCollection<CameraInterface>();
            DevicesList1 = new ObservableCollection<CameraDevice>();
            DevicesList2 = new ObservableCollection<CameraDevice>();
            LogMessages = new ObservableCollection<LogMessage>();

            EnumInterfaceCommand = new RelayCommand(EnumInterface, () => CanEnumInterface);
            OpenInterfaceCommand = new RelayCommand(OpenInterface, () => CanOpenInterface);
            CloseInterfaceCommand = new RelayCommand(CloseInterface, () => CanCloseInterface);
            EnumDeviceCommand = new RelayCommand(EnumDevice, () => CanEnumDevice);
            OpenDeviceCommand = new RelayCommand(OpenDevice, () => CanOpenDevice);
            CloseDeviceCommand = new RelayCommand(CloseDevice, () => CanCloseDevice);
            StartGrabCommand = new RelayCommand(StartGrab, () => CanStartGrab);
            StopGrabCommand = new RelayCommand(StopGrab, () => CanStopGrab);

            AddLogMessage("Application started", LogLevel.Info);
        }

        private void AddLogMessage(string message, LogLevel level = LogLevel.Info)
        {
            App.Current.Dispatcher.Invoke(() =>
            {
                LogMessages.Add(new LogMessage(message, level));
            });
        }

        private void UpdateCanExecuteStates()
        {
            OnPropertyChanged(nameof(CanEnumInterface));
            OnPropertyChanged(nameof(CanOpenInterface));
            OnPropertyChanged(nameof(CanCloseInterface));
            OnPropertyChanged(nameof(CanEnumDevice));
            OnPropertyChanged(nameof(CanOpenDevice));
            OnPropertyChanged(nameof(CanCloseDevice));
            OnPropertyChanged(nameof(CanStartGrab));
            OnPropertyChanged(nameof(CanStopGrab));
        }

        private void EnumInterface()
        {
            int nRet = 0;
            bool bChanged = false;

            // Enum interface
            nRet = _cSystem.UpdateInterfaceList(
                CParamDefine.MV_FG_CAMERALINK_INTERFACE | CParamDefine.MV_FG_GEV_INTERFACE |
                CParamDefine.MV_FG_CXP_INTERFACE | CParamDefine.MV_FG_XoF_INTERFACE,
                ref bChanged);

            if (CErrorCode.MV_FG_SUCCESS != nRet)
            {
                AddLogMessage($"Enum Interface failed, ErrorCode: {nRet:X}", LogLevel.Error);
                return;
            }

            _interfaceNum = 0;
            nRet = _cSystem.GetNumInterfaces(ref _interfaceNum);
            if (CErrorCode.MV_FG_SUCCESS != nRet)
            {
                AddLogMessage($"Get interface number failed, ErrorCode: {nRet:X}", LogLevel.Error);
                UpdateCanExecuteStates();
                return;
            }

            if (0 == _interfaceNum)
            {
                AddLogMessage("No interface found", LogLevel.Warning);
                UpdateCanExecuteStates();
                return;
            }

            if (bChanged)
            {
                AvailableInterfaces.Clear();

                MV_FG_INTERFACE_INFO stIfInfo = new MV_FG_INTERFACE_INFO();
                for (uint i = 0; i < _interfaceNum; i++)
                {
                    nRet = _cSystem.GetInterfaceInfo(i, ref stIfInfo);
                    if (CErrorCode.MV_FG_SUCCESS != nRet)
                    {
                        AvailableInterfaces.Clear();
                        AddLogMessage($"Get interface info failed, ErrorCode: {nRet:X}", LogLevel.Error);
                        return;
                    }

                    var interfaceModel = new CameraInterface
                    {
                        Index = i,
                        InterfaceType = stIfInfo.nTLayerType
                    };

                    switch (stIfInfo.nTLayerType)
                    {
                        case CParamDefine.MV_FG_GEV_INTERFACE:
                            {
                                var stGevIFInfo = (MV_GEV_INTERFACE_INFO)CAdditional.ByteToStruct(
                                    stIfInfo.SpecialInfo.stGevIfInfo, typeof(MV_GEV_INTERFACE_INFO));
                                interfaceModel.DisplayName = stGevIFInfo.chDisplayName;
                                interfaceModel.InterfaceId = stGevIFInfo.chInterfaceID;
                                interfaceModel.SerialNumber = stGevIFInfo.chSerialNumber;
                                break;
                            }
                        case CParamDefine.MV_FG_CXP_INTERFACE:
                            {
                                var stCxpIFInfo = (MV_CXP_INTERFACE_INFO)CAdditional.ByteToStruct(
                                    stIfInfo.SpecialInfo.stCXPIfInfo, typeof(MV_CXP_INTERFACE_INFO));
                                interfaceModel.DisplayName = stCxpIFInfo.chDisplayName;
                                interfaceModel.InterfaceId = stCxpIFInfo.chInterfaceID;
                                interfaceModel.SerialNumber = stCxpIFInfo.chSerialNumber;
                                break;
                            }
                        case CParamDefine.MV_FG_CAMERALINK_INTERFACE:
                            {
                                var stCmlIFInfo = (MV_CML_INTERFACE_INFO)CAdditional.ByteToStruct(
                                    stIfInfo.SpecialInfo.stCMLIfInfo, typeof(MV_CML_INTERFACE_INFO));
                                interfaceModel.DisplayName = stCmlIFInfo.chDisplayName;
                                interfaceModel.InterfaceId = stCmlIFInfo.chInterfaceID;
                                interfaceModel.SerialNumber = stCmlIFInfo.chSerialNumber;
                                break;
                            }
                        case CParamDefine.MV_FG_XoF_INTERFACE:
                            {
                                var stXoFIFInfo = (MV_XoF_INTERFACE_INFO)CAdditional.ByteToStruct(
                                    stIfInfo.SpecialInfo.stXoFIfInfo, typeof(MV_XoF_INTERFACE_INFO));
                                interfaceModel.DisplayName = stXoFIFInfo.chDisplayName;
                                interfaceModel.InterfaceId = stXoFIFInfo.chInterfaceID;
                                interfaceModel.SerialNumber = stXoFIFInfo.chSerialNumber;
                                break;
                            }
                        default:
                            interfaceModel.DisplayName = $"Unknown interface[{i}]";
                            break;
                    }

                    AvailableInterfaces.Add(interfaceModel);
                }
            }

            if (_interfaceNum > 0)
            {
                SelectedInterface1 = AvailableInterfaces.FirstOrDefault();
                SelectedInterface2 = AvailableInterfaces.FirstOrDefault();
            }

            UpdateCanExecuteStates();
            AddLogMessage($"Found {_interfaceNum} interfaces", LogLevel.Success);
        }

        private void OpenInterface()
        {
            if (SelectedInterface1 == null || SelectedInterface2 == null)
            {
                AddLogMessage("No interface selected", LogLevel.Warning);
                return;
            }

            int nRet = 0;

            // Open first interface
            nRet = _cSystem.OpenInterface(SelectedInterface1.Index, out _cInterface[0]);
            if (CErrorCode.MV_FG_SUCCESS != nRet)
            {
                AddLogMessage($"List 1: Open Interface failed, ErrorCode: {nRet:X}", LogLevel.Error);
                return;
            }

            _isInterfaceOpen[0] = true;
            SelectedInterface1.IsOpen = true;

            // Open second interface if different from first
            if (SelectedInterface1.Index != SelectedInterface2.Index)
            {
                nRet = _cSystem.OpenInterface(SelectedInterface2.Index, out _cInterface[1]);
                if (CErrorCode.MV_FG_SUCCESS != nRet)
                {
                    AddLogMessage($"List 2: Open Interface failed, ErrorCode: {nRet:X}", LogLevel.Error);
                    return;
                }
            }
            else
            {
                _cInterface[1] = _cInterface[0];
            }

            _isInterfaceOpen[1] = true;
            SelectedInterface2.IsOpen = true;

            UpdateCanExecuteStates();
            AddLogMessage("Interfaces opened successfully", LogLevel.Success);
        }

        private void CloseInterface()
        {
            int nRet = 0;
            _isGrabbing = false;

            if (_cInterface[0] == null && _cInterface[1] == null)
            {
                return;
            }

            if (_isDeviceOpen[0] || _isDeviceOpen[1])
            {
                CloseDevice();
            }

            // Close first interface
            if (_cInterface[0] != null)
            {
                nRet = _cInterface[0].CloseInterface();
                if (CErrorCode.MV_FG_SUCCESS != nRet)
                {
                    AddLogMessage($"List 1: Close interface failed, ErrorCode: {nRet:X}", LogLevel.Error);
                    return;
                }
            }

            // Close second interface if different from first
            if (_cInterface[1] != null && _cInterface[1] != _cInterface[0])
            {
                nRet = _cInterface[1].CloseInterface();
                if (CErrorCode.MV_FG_SUCCESS != nRet)
                {
                    AddLogMessage($"List 2: Close interface failed, ErrorCode: {nRet:X}", LogLevel.Error);
                    return;
                }
            }

            for (uint i = 0; i < LIST_NUM; i++)
            {
                _cInterface[i] = null;
                _isInterfaceOpen[i] = false;
            }

            if (SelectedInterface1 != null) SelectedInterface1.IsOpen = false;
            if (SelectedInterface2 != null) SelectedInterface2.IsOpen = false;

            UpdateCanExecuteStates();
            AddLogMessage("Interfaces closed successfully", LogLevel.Success);
        }

        private void EnumDevice()
        {
            int nRet = 0;
            bool[] bChanged = { false, false };
            uint[] nDeviceNum = { 0, 0 };

            // Enum devices on first interface
            nRet = _cInterface[0].UpdateDeviceList(ref bChanged[0]);
            if (CErrorCode.MV_FG_SUCCESS != nRet)
            {
                AddLogMessage($"List 1: Update device list failed, ErrorCode: {nRet:X}", LogLevel.Error);
                return;
            }

            nRet = _cInterface[0].GetNumDevices(ref nDeviceNum[0]);
            if (CErrorCode.MV_FG_SUCCESS != nRet)
            {
                AddLogMessage($"List 1: Get devices number failed, ErrorCode: {nRet:X}", LogLevel.Error);
                UpdateCanExecuteStates();
                return;
            }

            // Enum devices on second interface if different
            if (_cInterface[1] != null && _cInterface[1] != _cInterface[0])
            {
                nRet = _cInterface[1].UpdateDeviceList(ref bChanged[1]);
                if (CErrorCode.MV_FG_SUCCESS != nRet)
                {
                    AddLogMessage($"List 2: Update device list failed, ErrorCode: {nRet:X}", LogLevel.Error);
                    return;
                }

                nRet = _cInterface[1].GetNumDevices(ref nDeviceNum[1]);
                if (CErrorCode.MV_FG_SUCCESS != nRet)
                {
                    AddLogMessage($"List 2: Get devices number failed, ErrorCode: {nRet:X}", LogLevel.Error);
                    UpdateCanExecuteStates();
                    return;
                }
            }

            if (nDeviceNum[0] == 0 && nDeviceNum[1] == 0)
            {
                AddLogMessage("No Device found", LogLevel.Warning);
                UpdateCanExecuteStates();
                return;
            }

            if (bChanged[0] || bChanged[1])
            {
                DevicesList1.Clear();
                DevicesList2.Clear();

                // Process devices for both lists
                MV_FG_DEVICE_INFO stDeviceInfo = new MV_FG_DEVICE_INFO();

                // Add devices from first interface
                for (uint i = 0; i < nDeviceNum[0]; i++)
                {
                    nRet = _cInterface[0].GetDeviceInfo(i, ref stDeviceInfo);
                    if (CErrorCode.MV_FG_SUCCESS != nRet)
                    {
                        AddLogMessage($"List 1: Get device info failed, ErrorCode: {nRet:X}", LogLevel.Error);
                        return;
                    }

                    var device = CreateDeviceModel(stDeviceInfo, i, 0);
                    DevicesList1.Add(device);
                }

                // Add devices from second interface
                for (uint i = 0; i < nDeviceNum[1]; i++)
                {
                    nRet = _cInterface[1].GetDeviceInfo(i, ref stDeviceInfo);
                    if (CErrorCode.MV_FG_SUCCESS != nRet)
                    {
                        AddLogMessage($"List 2: Get device info failed, ErrorCode: {nRet:X}", LogLevel.Error);
                        return;
                    }

                    var device = CreateDeviceModel(stDeviceInfo, i, 1);
                    DevicesList2.Add(device);
                }

                // Fill remaining slots with empty devices
                for (uint i = nDeviceNum[0]; i < MAX_DEVICE_NUM; i++)
                {
                    DevicesList1.Add(new CameraDevice { Index = i, ListIndex = 0 });
                }

                for (uint i = nDeviceNum[1]; i < MAX_DEVICE_NUM; i++)
                {
                    DevicesList2.Add(new CameraDevice { Index = i, ListIndex = 1 });
                }
            }

            AddLogMessage($"List 1: Total Find {nDeviceNum[0]} devices!", LogLevel.Info);
            AddLogMessage($"List 2: Total Find {nDeviceNum[1]} devices!", LogLevel.Info);

            UpdateCanExecuteStates();
        }

        private CameraDevice CreateDeviceModel(MV_FG_DEVICE_INFO stDeviceInfo, uint index, uint listIndex)
        {
            var device = new CameraDevice
            {
                Index = index,
                ListIndex = listIndex,
                DeviceType = stDeviceInfo.nDevType
            };

            switch (stDeviceInfo.nDevType)
            {
                case CParamDefine.MV_FG_GEV_DEVICE:
                    {
                        var stGevDevInfo = (MV_GEV_DEVICE_INFO)CAdditional.ByteToStruct(
                            stDeviceInfo.DevInfo.stGEVDevInfo, typeof(MV_GEV_DEVICE_INFO));
                        device.ModelName = stGevDevInfo.chModelName;
                        device.SerialNumber = stGevDevInfo.chSerialNumber;
                        break;
                    }
                case CParamDefine.MV_FG_CXP_DEVICE:
                    {
                        var stCxpDevInfo = (MV_CXP_DEVICE_INFO)CAdditional.ByteToStruct(
                            stDeviceInfo.DevInfo.stCXPDevInfo, typeof(MV_CXP_DEVICE_INFO));
                        device.ModelName = stCxpDevInfo.chModelName;
                        device.SerialNumber = stCxpDevInfo.chSerialNumber;
                        break;
                    }
                case CParamDefine.MV_FG_CAMERALINK_DEVICE:
                    {
                        var stCmlDevInfo = (MV_CML_DEVICE_INFO)CAdditional.ByteToStruct(
                            stDeviceInfo.DevInfo.stCMLDevInfo, typeof(MV_CML_DEVICE_INFO));
                        device.ModelName = stCmlDevInfo.chModelName;
                        device.SerialNumber = stCmlDevInfo.chSerialNumber;
                        break;
                    }
                case CParamDefine.MV_FG_XoF_DEVICE:
                    {
                        var stXoFDevInfo = (MV_XoF_DEVICE_INFO)CAdditional.ByteToStruct(
                            stDeviceInfo.DevInfo.stXoFDevInfo, typeof(MV_XoF_DEVICE_INFO));
                        device.ModelName = stXoFDevInfo.chModelName;
                        device.SerialNumber = stXoFDevInfo.chSerialNumber;
                        break;
                    }
                default:
                    device.ModelName = $"Unknown device[{index}]";
                    break;
            }

            return device;
        }

        private void OpenDevice()
        {
            if (_isDeviceOpen[0] || _isDeviceOpen[1])
            {
                return;
            }

            bool[] bHaveCheck = { false, false };

            // Open devices for list 1
            foreach (var device in DevicesList1.Where(d => d.IsSelected && !string.IsNullOrEmpty(d.ModelName)))
            {
                bHaveCheck[0] = true;
                int nRet = _cInterface[0].OpenDevice(device.Index, out _cDevice[0, device.Index]);
                if (CErrorCode.MV_FG_SUCCESS != nRet)
                {
                    AddLogMessage($"List 1: Open device failed! DevIndex[{device.Index}] nRet {nRet:X}", LogLevel.Error);
                    _cDevice[0, device.Index] = null;
                    continue;
                }
                else
                {
                    AddLogMessage($"List 1: Open device success! DevIndex[{device.Index}]", LogLevel.Success);
                    device.IsOpen = true;
                    device.CDevice = _cDevice[0, device.Index];
                    _isDeviceOpen[0] = true;
                }

                // Set continuous acquisition mode
                var cDeviceParam = new CParam(_cDevice[0, device.Index]);
                cDeviceParam.SetEnumValue("AcquisitionMode", 2);  // 0 - SingleFrame, 2 - Continuous
                cDeviceParam.SetEnumValue("TriggerMode", TRIGGER_MODE_OFF);
            }

            // Open devices for list 2
            foreach (var device in DevicesList2.Where(d => d.IsSelected && !string.IsNullOrEmpty(d.ModelName)))
            {
                bHaveCheck[1] = true;
                int nRet = _cInterface[1].OpenDevice(device.Index, out _cDevice[1, device.Index]);
                if (CErrorCode.MV_FG_SUCCESS != nRet)
                {
                    AddLogMessage($"List 2: Open device failed! DevIndex[{device.Index}] nRet {nRet:X}", LogLevel.Error);
                    _cDevice[1, device.Index] = null;
                    continue;
                }
                else
                {
                    AddLogMessage($"List 2: Open device success! DevIndex[{device.Index}]", LogLevel.Success);
                    device.IsOpen = true;
                    device.CDevice = _cDevice[1, device.Index];
                    _isDeviceOpen[1] = true;
                }

                // Set continuous acquisition mode
                var cDeviceParam = new CParam(_cDevice[1, device.Index]);
                cDeviceParam.SetEnumValue("AcquisitionMode", 2);  // 0 - SingleFrame, 2 - Continuous
                cDeviceParam.SetEnumValue("TriggerMode", TRIGGER_MODE_OFF);
            }

            // Update UI state
            if (_isDeviceOpen[0])
            {
                foreach (var device in DevicesList1)
                {
                    device.IsSelected = false; // Disable selection after opening
                }
            }
            else if (!bHaveCheck[0])
            {
                AddLogMessage("List 1: Unchecked device!", LogLevel.Warning);
            }
            else
            {
                AddLogMessage("List 1: No device opened successfully!", LogLevel.Warning);
            }

            if (_isDeviceOpen[1])
            {
                foreach (var device in DevicesList2)
                {
                    device.IsSelected = false; // Disable selection after opening
                }
            }
            else if (!bHaveCheck[1])
            {
                AddLogMessage("List 2: Unchecked device!", LogLevel.Warning);
            }
            else
            {
                AddLogMessage("List 2: No device opened successfully!", LogLevel.Warning);
            }

            UpdateCanExecuteStates();
        }

        private void CloseDevice()
        {
            if (!_isDeviceOpen[0] && !_isDeviceOpen[1])
            {
                return;
            }

            StopGrab();
            int nRet = 0;

            for (int i = 0; i < LIST_NUM; i++)
            {
                for (int j = 0; j < MAX_DEVICE_NUM; j++)
                {
                    if (_cDevice[i, j] != null)
                    {
                        nRet = _cDevice[i, j].CloseDevice();
                        if (CErrorCode.MV_FG_SUCCESS != nRet)
                        {
                            AddLogMessage($"List {i + 1}: Close device fail! DevIndex[{j + 1}], nRet {nRet:X}", LogLevel.Error);
                        }
                        _cDevice[i, j] = null;
                    }
                }
            }

            // Update device states
            foreach (var device in DevicesList1)
            {
                device.IsOpen = false;
                device.IsSelected = true; // Re-enable selection
                device.CDevice = null;
            }

            foreach (var device in DevicesList2)
            {
                device.IsOpen = false;
                device.IsSelected = true; // Re-enable selection
                device.CDevice = null;
            }

            _isDeviceOpen[0] = false;
            _isDeviceOpen[1] = false;
            UpdateCanExecuteStates();
            AddLogMessage("All devices closed", LogLevel.Success);
        }

        private void StartGrab()
        {
            if ((!_isDeviceOpen[0] && !_isDeviceOpen[1]) || _isGrabbing)
            {
                return;
            }

            int nRet = 0;

            for (int i = 0; i < LIST_NUM; i++)
            {
                for (int j = 0; j < MAX_DEVICE_NUM; j++)
                {
                    if (_cDevice[i, j] != null)
                    {
                        // Get number of streams
                        uint nStreamNum = 0;
                        nRet = _cDevice[i, j].GetNumStreams(ref nStreamNum);
                        if (CErrorCode.MV_FG_SUCCESS != nRet || nStreamNum == 0)
                        {
                            AddLogMessage($"List {i + 1}: No stream available! DevIndex[{j + 1}], nRet {nRet:X}", LogLevel.Error);
                            continue;
                        }

                        // Open stream
                        nRet = _cDevice[i, j].OpenStream(0, out _cStream[i, j]);
                        if (CErrorCode.MV_FG_SUCCESS != nRet)
                        {
                            AddLogMessage($"List {i + 1}: Open Stream failed! DevIndex[{j + 1}], nRet {nRet:X}", LogLevel.Error);
                            continue;
                        }

                        // Set buffer number
                        nRet = _cStream[i, j].SetBufferNum(BUFFER_NUMBER);
                        if (CErrorCode.MV_FG_SUCCESS != nRet)
                        {
                            AddLogMessage($"List {i + 1}: Set buffer number failed! DevIndex[{j + 1}], nRet {nRet:X}", LogLevel.Error);
                            _cStream[i, j].CloseStream();
                            _cStream[i, j] = null;
                            continue;
                        }

                        // Start acquisition
                        nRet = _cStream[i, j].StartAcquisition();
                        if (CErrorCode.MV_FG_SUCCESS != nRet)
                        {
                            AddLogMessage($"List {i + 1}: Start Acquisition failed! DevIndex[{j + 1}], nRet {nRet:X}", LogLevel.Error);
                            _cStream[i, j].CloseStream();
                            _cStream[i, j] = null;
                            continue;
                        }

                        _currentListIndex = i;
                        _currentCameraIndex = j;
                        _isGrabbing = true;

                        // Create acquisition thread
                        _hGrabThread[i, j] = new Thread(() => ReceiveThreadProcess(i, j));
                        if (_hGrabThread[i, j] == null)
                        {
                            _isGrabbing = false;
                            AddLogMessage("Create thread failed", LogLevel.Error);
                            return;
                        }
                        _hGrabThread[i, j].Start();
                        _bGrabThreadFlag[i, j] = true;

                        // Update device state
                        var deviceList = i == 0 ? DevicesList1 : DevicesList2;
                        var device = deviceList.FirstOrDefault(d => d.Index == j);
                        if (device != null)
                        {
                            device.IsGrabbing = true;
                            device.CStream = _cStream[i, j];
                        }
                    }
                }
            }

            UpdateCanExecuteStates();
            AddLogMessage("Started grabbing", LogLevel.Success);
        }

        private void StopGrab()
        {
            if ((!_isDeviceOpen[0] && !_isDeviceOpen[1]) || !_isGrabbing)
            {
                return;
            }

            int nRet = 0;

            for (int i = 0; i < LIST_NUM; i++)
            {
                for (int j = 0; j < MAX_DEVICE_NUM; j++)
                {
                    if (_hGrabThread[i, j] == null)
                    {
                        continue;
                    }

                    _bGrabThreadFlag[i, j] = false;
                    _hGrabThread[i, j].Join();

                    if (_cStream[i, j] != null)
                    {
                        nRet = _cStream[i, j].StopAcquisition();
                        if (CErrorCode.MV_FG_SUCCESS != nRet)
                        {
                            AddLogMessage($"List {i + 1}: Stop grabbing failed! DevIndex[{j + 1}], nRet {nRet:X}", LogLevel.Error);
                            return;
                        }

                        nRet = _cStream[i, j].CloseStream();
                        if (CErrorCode.MV_FG_SUCCESS != nRet)
                        {
                            AddLogMessage($"List {i + 1}: Close stream failed! nRet {nRet:X}", LogLevel.Error);
                            return;
                        }
                        _cStream[i, j] = null;

                        // Update device state
                        var deviceList = i == 0 ? DevicesList1 : DevicesList2;
                        var device = deviceList.FirstOrDefault(d => d.Index == j);
                        if (device != null)
                        {
                            device.IsGrabbing = false;
                            device.CStream = null;
                        }
                    }
                }
            }

            _isGrabbing = false;
            UpdateCanExecuteStates();
            AddLogMessage("Stopped grabbing", LogLevel.Success);
        }

        private void ReceiveThreadProcess(int listIndex, int cameraIndex)
        {
            const uint nTimeout = 1000;
            var stFrameInfo = new MV_FG_BUFFER_INFO();
            var stDisplayFrameInfo = new MV_FG_INPUT_IMAGE_INFO_EX();

            var cImgProc = new CImageProcess(_cStream[listIndex, cameraIndex]);
            int nRet = 0;

            while (_isGrabbing && _bGrabThreadFlag[listIndex, cameraIndex])
            {
                nRet = _cStream[listIndex, cameraIndex].GetFrameBuffer(ref stFrameInfo, nTimeout);
                if (CErrorCode.MV_FG_SUCCESS != nRet)
                {
                    AddLogMessage($"Get Frame Buffer fail! DevIndex[{cameraIndex}] nRet {nRet:X}", LogLevel.Error);
                    continue;
                }

                if (stFrameInfo.pBuffer != null)
                {
                    stDisplayFrameInfo.pImageBuf = stFrameInfo.pBuffer;
                    stDisplayFrameInfo.nImageBufLen = stFrameInfo.nFilledSize;
                    stDisplayFrameInfo.nWidth = stFrameInfo.nWidth;
                    stDisplayFrameInfo.nHeight = stFrameInfo.nHeight;
                    stDisplayFrameInfo.enPixelType = stFrameInfo.enPixelType;

                    // Note: For WPF, we would need to convert this to a WPF-compatible image format
                    // This is a simplified version - actual implementation would require image conversion

                    nRet = _cStream[listIndex, cameraIndex].ReleaseFrameBuffer(stFrameInfo);
                    if (CErrorCode.MV_FG_SUCCESS != nRet)
                    {
                        AddLogMessage($"Release frame buffer failed! nRet {nRet:X}", LogLevel.Error);
                    }
                }
            }
        }
    }
}
