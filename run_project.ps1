# PowerShell script to run MultipleCamera WPF project

Write-Host "=== MultipleCamera WPF Project Runner ===" -ForegroundColor Green
Write-Host ""

# Check if executable exists
$exePath = "bin\Debug\MultipleCamera.exe"
if (Test-Path $exePath) {
    Write-Host "Found existing executable at: $exePath" -ForegroundColor Yellow
    Write-Host "Running application..." -ForegroundColor Green
    Start-Process -FilePath $exePath -Wait
} else {
    Write-Host "Executable not found. Attempting to build project..." -ForegroundColor Yellow
    
    # Try to find Visual Studio and MSBuild
    $msbuildPaths = @(
        "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
        "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
        "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
        "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe",
        "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"
    )
    
    $msbuildFound = $false
    foreach ($path in $msbuildPaths) {
        if (Test-Path $path) {
            Write-Host "Found MSBuild at: $path" -ForegroundColor Green
            Write-Host "Building solution..." -ForegroundColor Yellow
            
            & $path "MultipleCamera.sln" "/p:Configuration=Debug" "/p:Platform=Any CPU" "/verbosity:minimal"
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Build successful!" -ForegroundColor Green
                if (Test-Path $exePath) {
                    Write-Host "Running application..." -ForegroundColor Green
                    Start-Process -FilePath $exePath -Wait
                } else {
                    Write-Host "Build completed but executable not found!" -ForegroundColor Red
                }
            } else {
                Write-Host "Build failed with exit code: $LASTEXITCODE" -ForegroundColor Red
            }
            $msbuildFound = $true
            break
        }
    }
    
    if (-not $msbuildFound) {
        Write-Host "MSBuild not found!" -ForegroundColor Red
        Write-Host ""
        Write-Host "Please try one of the following options:" -ForegroundColor Yellow
        Write-Host "1. Open MultipleCamera.sln in Visual Studio and build from there" -ForegroundColor White
        Write-Host "2. Install Visual Studio Build Tools" -ForegroundColor White
        Write-Host "3. Use Visual Studio Developer Command Prompt" -ForegroundColor White
        Write-Host ""
        Write-Host "Opening solution file in default application..." -ForegroundColor Yellow
        Start-Process "MultipleCamera.sln"
    }
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
